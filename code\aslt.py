import numpy as np

def aslt(input_data, Fs, F, Ncyc, ord=None, mult=False):
    """
    Computes the adaptive superresolution wavelet (superlet) transform on 
    input data to produce a time-frequency representation. For each 
    frequency of interest, the closest integer order from the order 
    interval will be chosen to produce each superlet. A superlet is a set 
    of wavelets with the same center frequency but different number of 
    cycles.

    REFERENCE:
    Superlets: time-frequency super-resolution using wavelet sets
    Moca, V.V., Nagy-<PERSON>, A., Bârz<PERSON>, H., Mureșan, R.C.
    https://www.biorxiv.org/content/10.1101/583732v1.full

    Args:
        input_data (np.ndarray): Input data matrix of shape [buffers x samples].
        Fs (float): Sampling frequency in Hz.
        F (np.ndarray): Array of frequencies of interest.
        Ncyc (float): Number of initial wavelet cycles.
        ord (list or tuple, optional): [min_order, max_order] interval of 
                                       superresolution orders. Defaults to None, which
                                       results in a standard CWT (order=1).
        mult (bool, optional): Specifies the use of multiplicative superresolution.
                               False for additive, True for multiplicative. Defaults to False.

    Returns:
        np.ndarray: The [frequencies x samples] superlet spectrum.
    """

    # --- Input Validation ---
    if F is None or len(F) == 0:
        raise ValueError("Frequencies not defined.")

    if input_data is None or input_data.size == 0:
        raise ValueError("Input data is empty.")

    # Ensure input_data is a 2D array
    if input_data.ndim == 1:
        input_data = input_data[np.newaxis, :]

    # --- Initialization ---
    if ord is not None and len(ord) == 2:
        order_ls = np.round(np.linspace(ord[0], ord[1], len(F))).astype(int)
    else:
        # Default to order 1 for all frequencies (standard CWT)
        order_ls = np.ones(len(F), dtype=int)

    Nbuffers, Npoints = input_data.shape
    padding = 0
    
    # Create a list of lists to store the wavelets
    wavelets = [[] for _ in range(len(F))]

    # --- Generate Wavelet Sets ---
    if mult:
        # Multiplicative superresolution
        for i_freq in range(len(F)):
            for i_ord in range(1, order_ls[i_freq] + 1):
                # Each new wavelet has Ncyc extra cycles
                w = cxmorlet(F[i_freq], Ncyc * i_ord, Fs)
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) / 2))
    else:
        # Additive superresolution
        for i_freq in range(len(F)):
            for i_ord in range(order_ls[i_freq]):
                # Each new wavelet has an extra cycle
                w = cxmorlet(F[i_freq], Ncyc + i_ord, Fs)
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) / 2))

    # --- Main Computation ---
    buffer = np.zeros(Npoints + 2 * padding)
    wtresult = np.zeros((len(F), Npoints))
    
    # Convenience indices for the zero-padded buffer
    bufbegin = padding
    bufend = padding + Npoints

    for i_buf in range(Nbuffers):
        for i_freq in range(len(F)):
            # Pooling buffer for geometric mean, starts with 1
            temp = np.ones(Npoints)
            
            # Fill the central part of the buffer with input data
            buffer[bufbegin:bufend] = input_data[i_buf, :]
            
            # Convolve with each wavelet in the current set
            for i_ord in range(order_ls[i_freq]):
                # Restricted convolution (input size == output size)
                tempcx = np.convolve(buffer, wavelets[i_freq][i_ord], mode='same')
                
                # Accumulate the squared magnitude
                # (times 2 to get the full spectral energy)
                temp *= (2 * np.abs(tempcx[bufbegin:bufend]))**2

            # Compute the power of the geometric mean
            root = 1 / order_ls[i_freq]
            temp = temp ** root
            
            # Accumulate the result for the current frequency
            wtresult[i_freq, :] += temp

    # Scale the output by the number of input buffers
    wtresult /= Nbuffers
    
    return wtresult


def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet.

    Args:
        Fc (float): Center frequency.
        Nc (float): Number of cycles.
        Fs (float): Sampling frequency.

    Returns:
        np.ndarray: The complex Morlet wavelet.
    """
    # The last peak should be at 2.5 standard deviations
    sd = (Nc / 2) * (1 / Fc) / 2.5
    wl = 2 * np.floor(int(6 * sd * Fs) / 2) + 1
    w = np.zeros(int(wl))
    gi = 0
    off = int(wl / 2)
    
    for i in range(int(wl)):
        t = (i - off) / Fs
        w[i] = bw_cf(t, sd, Fc)
        gi += gauss(t, sd)
        
    return w / gi

def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients.
    """
    cnorm = 1 / (bw * np.sqrt(2 * np.pi))
    exp1 = cnorm * np.exp(-(t**2) / (2 * bw**2))
    return np.exp(2j * np.pi * cf * t) * exp1

def gauss(t, sd):
    """
    Computes the Gaussian coefficient.
    """
    cnorm = 1 / (sd * np.sqrt(2 * np.pi))
    return cnorm * np.exp(-(t**2) / (2 * sd**2))