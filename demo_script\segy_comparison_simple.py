#!/usr/bin/env python3
"""
Simplified SEG-Y Superlet vs DLOGST Comparison
A streamlined version for easy testing and demonstration.
"""

import numpy as np
import matplotlib.pyplot as plt
import segyio
import tkinter as tk
from tkinter import filedialog
from superletcx import superlets
from reference.dlogst_spec_descriptor import dlogst_spec_descriptor

def load_segy_data():
    """Load SEG-Y data using file dialog."""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="Select SEG-Y file", 
        filetypes=[("SEG-Y files", "*.sgy"), ("All files", "*.*")]
    )
    if not file_path:
        print("No file selected. Exiting.")
        exit()
    
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segyio.tools.collect(segy.trace[:])
        dt = segy.bin[segyio.BinField.Interval] / 1_000_000
        num_samples = data.shape[1]
        time = np.arange(num_samples) * dt
    
    return data, time, dt

def create_comparison_plot(signal, time, fs, trace_name, 
                          freq_range=(5, 100), 
                          c1=3, orders=(1, 10),
                          shape=0.35, kmax=120, int_val=35,
                          colormap='viridis'):
    """Create the 7-column comparison plot for a single trace."""
    
    dt = 1.0 / fs
    fmax_samples = int(freq_range[1] * len(signal) / fs)
    
    # Frequency array for superlet
    freqs = np.linspace(freq_range[0], freq_range[1], 80)
    
    print(f"Computing Superlet Transform for {trace_name}...")
    # Compute Superlet Transform
    superlet_spectrum = superlets(signal, fs, freqs, c1, orders)
    superlet_magnitude = np.abs(superlet_spectrum)
    superlet_voice = np.real(superlet_spectrum)  # Real component for voice
    superlet_mag_voice = superlet_magnitude * superlet_voice
    
    print(f"Computing DLOGST Spectral Descriptor for {trace_name}...")
    # Compute DLOGST
    MST, dlogst_mag, dlogst_phase, dlogst_voice, peak_freq, freq_loc, spec_centroid, \
    spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
        dlogst_spec_descriptor(signal, dt, fmax_samples, shape, kmax, int_val)
    
    dlogst_mag_voice = dlogst_mag * dlogst_voice
    
    # Limit frequency range for DLOGST to match superlet range
    freq_limit = np.argmin(np.abs(freqst - freq_range[1]))
    
    # Create the 7-column plot
    fig, axes = plt.subplots(1, 7, figsize=(28, 10), sharey=True)
    fig.suptitle(f'Superlet vs DLOGST Comparison - {trace_name}', fontsize=16)
    
    # Column 1: Original Signal
    axes[0].plot(signal, time, color='black', linewidth=0.8)
    axes[0].set_title('Original Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)
    
    # Columns 2-4: Superlet results
    # Magnitude TFR
    im1 = axes[1].pcolormesh(freqs, time, superlet_magnitude.T, 
                            shading='auto', cmap=colormap)
    axes[1].set_title('Superlet Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_xlim(freq_range)
    plt.colorbar(im1, ax=axes[1], shrink=0.8)
    
    # Voice TFR (real part with oscillation)
    vmax_superlet = np.max(np.abs(superlet_voice))
    im2 = axes[2].pcolormesh(freqs, time, superlet_voice.T, 
                            shading='auto', cmap='RdBu', 
                            vmin=-vmax_superlet, vmax=vmax_superlet)
    axes[2].set_title('Superlet Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_xlim(freq_range)
    plt.colorbar(im2, ax=axes[2], shrink=0.8)
    
    # Magnitude * Voice TFR
    im3 = axes[3].pcolormesh(freqs, time, superlet_mag_voice.T, 
                            shading='auto', cmap=colormap)
    axes[3].set_title('Superlet Mag×Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_xlim(freq_range)
    plt.colorbar(im3, ax=axes[3], shrink=0.8)
    
    # Columns 5-7: DLOGST results
    # Magnitude TFR
    im4 = axes[4].pcolormesh(freqst[:freq_limit], time, 
                            dlogst_mag[:freq_limit, :].T, 
                            shading='auto', cmap=colormap)
    axes[4].set_title('DLOGST Magnitude')
    axes[4].set_xlabel('Frequency (Hz)')
    axes[4].set_xlim(freq_range)
    plt.colorbar(im4, ax=axes[4], shrink=0.8)
    
    # Voice TFR
    vmax_dlogst = np.max(np.abs(dlogst_voice[:freq_limit, :]))
    im5 = axes[5].pcolormesh(freqst[:freq_limit], time, 
                            dlogst_voice[:freq_limit, :].T, 
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_dlogst, vmax=vmax_dlogst)
    axes[5].set_title('DLOGST Voice')
    axes[5].set_xlabel('Frequency (Hz)')
    axes[5].set_xlim(freq_range)
    plt.colorbar(im5, ax=axes[5], shrink=0.8)
    
    # Magnitude * Voice TFR
    im6 = axes[6].pcolormesh(freqst[:freq_limit], time, 
                            dlogst_mag_voice[:freq_limit, :].T, 
                            shading='auto', cmap=colormap)
    axes[6].set_title('DLOGST Mag×Voice')
    axes[6].set_xlabel('Frequency (Hz)')
    axes[6].set_xlim(freq_range)
    plt.colorbar(im6, ax=axes[6], shrink=0.8)
    
    # Set time limits for all axes (invert for seismic convention)
    for ax in axes:
        ax.set_ylim(time[-1], time[0])  # Reverse for seismic convention
        if ax != axes[0]:  # Don't set ylabel for signal plot
            ax.set_ylabel('')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    return fig, axes

def main():
    """Main function to process traces from SEG-Y data."""
    # Load SEG-Y data
    print("Loading SEG-Y data...")
    data, time, dt = load_segy_data()
    fs = 1.0 / dt
    
    print(f"Loaded SEG-Y file:")
    print(f"  Number of traces: {data.shape[0]}")
    print(f"  Samples per trace: {data.shape[1]}")
    print(f"  Sampling interval: {dt*1000:.2f} ms")
    print(f"  Total time: {time[-1]:.2f} s")
    
    # Default trace indices (modify as needed)
    trace_indices = {
        'W_BTP': 189,
        'B_1': 460,
        'TN_S253': 1432
    }
    
    # Validate and filter trace indices
    max_trace = data.shape[0] - 1
    valid_traces = {}
    for name, idx in trace_indices.items():
        if idx <= max_trace:
            valid_traces[name] = idx
        else:
            print(f"Warning: Trace {name} (index {idx}) exceeds available traces ({max_trace}). Skipping.")
    
    if not valid_traces:
        print("No valid traces found. Using first trace as default.")
        valid_traces = {'Trace_0': 0}
    
    # Parameters (can be modified as needed)
    freq_range = (5, 100)  # Frequency range for analysis
    c1 = 3  # Superlet base cycles
    orders = (1, 10)  # Superlet orders
    shape = 0.35  # DLOGST shape parameter
    kmax = 120  # DLOGST kmax parameter
    int_val = 35  # DLOGST intercept parameter
    colormap = 'viridis'  # Colormap for plots
    
    print(f"\nProcessing {len(valid_traces)} traces...")
    
    # Process each trace
    for trace_name, trace_index in valid_traces.items():
        print(f"\nProcessing trace {trace_name} (index {trace_index})...")
        trace_data = data[trace_index, :]
        
        # Create comparison plot
        fig, axes = create_comparison_plot(
            trace_data, time, fs, trace_name,
            freq_range=freq_range,
            c1=c1, orders=orders,
            shape=shape, kmax=kmax, int_val=int_val,
            colormap=colormap
        )
        
        # Save individual plot
        filename = f'segy_comparison_{trace_name}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"Plot saved as '{filename}'")
    
    print(f"\nProcessing complete. Generated {len(valid_traces)} comparison plots.")
    print(f"Parameters used:")
    print(f"  Frequency range: {freq_range[0]}-{freq_range[1]} Hz")
    print(f"  Superlet c1: {c1}, orders: {orders}")
    print(f"  DLOGST shape: {shape}, kmax: {kmax}, int_val: {int_val}")
    
    plt.show()

if __name__ == "__main__":
    main()
