#!/usr/bin/env python3
"""
Superlet vs DLOGST Spectral Descriptor Comparison
Creates a 7-column comparison plot showing:
1. Signal
2-4. Superlet: Magnitude, Voice, Magnitude*Voice
5-7. DLOGST: Magnitude, Voice, Magnitude*Voice
"""

import numpy as np
import matplotlib.pyplot as plt
import segyio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk
from superletcx import superlets
from reference.dlogst_spec_descriptor import dlogst_spec_descriptor

def load_segy_data():
    """Load SEG-Y data using file dialog."""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="Select SEG-Y file", 
        filetypes=[("SEG-Y files", "*.sgy"), ("All files", "*.*")]
    )
    if not file_path:
        print("No file selected. Exiting.")
        exit()
    
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segyio.tools.collect(segy.trace[:])
        dt = segy.bin[segyio.BinField.Interval] / 1_000_000
        num_samples = data.shape[1]
        time = np.arange(num_samples) * dt
    
    return data, time, dt

def get_trace_selection(data):
    """Get trace selection from user."""
    root = tk.Tk()
    root.title("Select Trace")
    
    trace_index = tk.IntVar(value=0)
    
    def on_ok():
        root.quit()
        root.destroy()
    
    tk.Label(root, text=f"Select trace index (0 to {data.shape[0]-1}):").pack(pady=5)
    trace_entry = tk.Entry(root, textvariable=trace_index)
    trace_entry.pack(pady=5)
    
    tk.Button(root, text="OK", command=on_ok).pack(pady=10)
    
    root.mainloop()
    return trace_index.get()

def get_plot_settings():
    """Get plot settings from user."""
    root = tk.Tk()
    root.title("Set Plot Parameters")
    
    settings = {}
    entries = {}
    
    def on_ok():
        for key, entry in entries.items():
            try:
                if key == 'colormap':
                    settings[key] = entry.get()
                else:
                    min_val, max_val = entry[0].get(), entry[1].get()
                    settings[key] = (float(min_val), float(max_val))
            except ValueError:
                print(f"Invalid input for {key}. Using default values.")
        root.quit()
        root.destroy()
    
    # Parameter fields
    fields = [
        ("Signal Amplitude", (-1000, 1000)),
        ("Frequency Range", (0, 100)),
        ("Time Range", (0, 5)),
        ("Magnitude Range", (0, 1)),
        ("Voice Range", (-1, 1)),
        ("Mag*Voice Range", (-500, 500))
    ]
    
    for i, (name, default) in enumerate(fields):
        tk.Label(root, text=f"{name}:").grid(row=i, column=0, sticky="e")
        min_entry = tk.Entry(root)
        min_entry.insert(0, str(default[0]))
        min_entry.grid(row=i, column=1)
        tk.Label(root, text="to").grid(row=i, column=2)
        max_entry = tk.Entry(root)
        max_entry.insert(0, str(default[1]))
        max_entry.grid(row=i, column=3)
        entries[name] = (min_entry, max_entry)
    
    # Colormap selection
    tk.Label(root, text="Colormap:").grid(row=len(fields), column=0, sticky="e")
    colormap_options = ['viridis', 'plasma', 'inferno', 'magma', 'cividis', 'jet', 'seismic']
    colormap_var = tk.StringVar(root)
    colormap_var.set(colormap_options[0])
    colormap_menu = ttk.OptionMenu(root, colormap_var, *colormap_options)
    colormap_menu.grid(row=len(fields), column=1, columnspan=3, sticky="ew")
    entries['colormap'] = colormap_var
    
    # Superlet parameters
    tk.Label(root, text="Superlet c1:").grid(row=len(fields)+1, column=0, sticky="e")
    c1_entry = tk.Entry(root)
    c1_entry.insert(0, "3")
    c1_entry.grid(row=len(fields)+1, column=1)
    entries['c1'] = c1_entry
    
    tk.Label(root, text="Superlet orders (min,max):").grid(row=len(fields)+2, column=0, sticky="e")
    order_min_entry = tk.Entry(root)
    order_min_entry.insert(0, "1")
    order_min_entry.grid(row=len(fields)+2, column=1)
    order_max_entry = tk.Entry(root)
    order_max_entry.insert(0, "10")
    order_max_entry.grid(row=len(fields)+2, column=3)
    entries['orders'] = (order_min_entry, order_max_entry)
    
    # DLOGST parameters
    tk.Label(root, text="DLOGST shape:").grid(row=len(fields)+3, column=0, sticky="e")
    shape_entry = tk.Entry(root)
    shape_entry.insert(0, "0.35")
    shape_entry.grid(row=len(fields)+3, column=1)
    entries['shape'] = shape_entry
    
    tk.Label(root, text="DLOGST kmax:").grid(row=len(fields)+4, column=0, sticky="e")
    kmax_entry = tk.Entry(root)
    kmax_entry.insert(0, "120")
    kmax_entry.grid(row=len(fields)+4, column=1)
    entries['kmax'] = kmax_entry
    
    tk.Label(root, text="DLOGST int_val:").grid(row=len(fields)+5, column=0, sticky="e")
    int_val_entry = tk.Entry(root)
    int_val_entry.insert(0, "35")
    int_val_entry.grid(row=len(fields)+5, column=1)
    entries['int_val'] = int_val_entry
    
    tk.Button(root, text="OK", command=on_ok).grid(row=len(fields)+6, column=1, columnspan=2)
    
    root.mainloop()
    return settings

def main():
    """Main function to create the comparison plot."""
    # Load data
    data, time, dt = load_segy_data()
    
    # Get trace selection
    trace_index = get_trace_selection(data)
    trace_data = data[trace_index, :]
    
    # Get plot settings
    settings = get_plot_settings()
    
    # Extract parameters
    fs = 1.0 / dt
    fmax_hz = settings['Frequency Range'][1]
    fmax_samples = int(fmax_hz * len(trace_data) / fs)
    
    # Frequency array for superlet
    freqs = np.linspace(1, fmax_hz, min(100, fmax_samples))
    
    # Superlet parameters
    c1 = int(settings['c1'])
    orders = (int(settings['orders'][0]), int(settings['orders'][1]))
    
    # DLOGST parameters
    shape = float(settings['shape'])
    kmax = float(settings['kmax'])
    int_val = float(settings['int_val'])
    
    print("Computing Superlet Transform...")
    # Compute Superlet Transform
    superlet_spectrum = superlets(trace_data, fs, freqs, c1, orders)
    superlet_magnitude = np.abs(superlet_spectrum)
    superlet_voice = np.real(superlet_spectrum)  # Real part as voice
    superlet_mag_voice = superlet_magnitude * superlet_voice
    
    print("Computing DLOGST Spectral Descriptor...")
    # Compute DLOGST
    MST, dlogst_mag, dlogst_phase, dlogst_voice, peak_freq, freq_loc, spec_centroid, \
    spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
        dlogst_spec_descriptor(trace_data, dt, fmax_samples, shape, kmax, int_val)
    
    dlogst_mag_voice = dlogst_mag * dlogst_voice
    
    # Limit frequency range for plotting
    freq_limit_superlet = len(freqs)
    freq_limit_dlogst = np.argmin(np.abs(freqst - fmax_hz))
    
    print("Creating comparison plot...")
    # Create the 7-column comparison plot
    fig, axes = plt.subplots(1, 7, figsize=(28, 10), sharey=True)
    fig.suptitle(f'Superlet vs DLOGST Comparison - Trace {trace_index}', fontsize=16)
    
    # Column 1: Signal
    axes[0].plot(trace_data, time)
    axes[0].set_title('Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].set_xlim(settings['Signal Amplitude'])
    
    # Columns 2-4: Superlet results
    # Magnitude
    im1 = axes[1].pcolormesh(freqs, time, superlet_magnitude.T, 
                            shading='auto', cmap=settings['colormap'])
    axes[1].set_title('Superlet Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_xlim(settings['Frequency Range'])
    plt.colorbar(im1, ax=axes[1])
    
    # Voice
    vmax_superlet = np.max(np.abs(superlet_voice))
    im2 = axes[2].pcolormesh(freqs, time, superlet_voice.T, 
                            shading='auto', cmap='RdBu', 
                            vmin=-vmax_superlet, vmax=vmax_superlet)
    axes[2].set_title('Superlet Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_xlim(settings['Frequency Range'])
    plt.colorbar(im2, ax=axes[2])
    
    # Magnitude * Voice
    im3 = axes[3].pcolormesh(freqs, time, superlet_mag_voice.T, 
                            shading='auto', cmap=settings['colormap'])
    axes[3].set_title('Superlet Mag*Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_xlim(settings['Frequency Range'])
    plt.colorbar(im3, ax=axes[3])
    
    # Columns 5-7: DLOGST results
    # Magnitude
    im4 = axes[4].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_mag[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap=settings['colormap'])
    axes[4].set_title('DLOGST Magnitude')
    axes[4].set_xlabel('Frequency (Hz)')
    axes[4].set_xlim(settings['Frequency Range'])
    plt.colorbar(im4, ax=axes[4])
    
    # Voice
    vmax_dlogst = np.max(np.abs(dlogst_voice[:freq_limit_dlogst, :]))
    im5 = axes[5].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_voice[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_dlogst, vmax=vmax_dlogst)
    axes[5].set_title('DLOGST Voice')
    axes[5].set_xlabel('Frequency (Hz)')
    axes[5].set_xlim(settings['Frequency Range'])
    plt.colorbar(im5, ax=axes[5])
    
    # Magnitude * Voice
    im6 = axes[6].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_mag_voice[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap=settings['colormap'])
    axes[6].set_title('DLOGST Mag*Voice')
    axes[6].set_xlabel('Frequency (Hz)')
    axes[6].set_xlim(settings['Frequency Range'])
    plt.colorbar(im6, ax=axes[6])
    
    # Set time limits for all axes
    for ax in axes:
        ax.set_ylim(settings['Time Range'][::-1])  # Reverse for seismic convention
        if ax != axes[0]:  # Don't set ylabel for signal plot
            ax.set_ylabel('')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    # Save the figure
    plt.savefig('superlet_vs_dlogst_comparison.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'superlet_vs_dlogst_comparison.png'")
    
    plt.show()

if __name__ == "__main__":
    main()
